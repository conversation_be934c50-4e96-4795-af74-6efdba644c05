{"rustc": 11410426090777951712, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 17119678153920512875, "deps": [[654232091421095663, "tauri_utils", false, 10022422225827242235], [4824857623768494398, "cargo_toml", false, 2711266338019540471], [4899080583175475170, "semver", false, 12462931904875540018], [6913375703034175521, "schemars", false, 5847459251798291741], [7170110829644101142, "json_patch", false, 16832709223554916395], [8569119365930580996, "serde_json", false, 6942067756965010790], [9090328626728818999, "toml", false, 778415984354411532], [9689903380558560274, "serde", false, 5351880197929074958], [12714016054753183456, "tauri_winres", false, 10864455024366441445], [13077543566650298139, "heck", false, 12143741045076549127], [13625485746686963219, "anyhow", false, 12208565187802399050], [15622660310229662834, "walkdir", false, 6540311310246530975], [16928111194414003569, "dirs", false, 5488783227525226273], [17155886227862585100, "glob", false, 18124508793629605632]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-build-edd936aac4ca5281/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}