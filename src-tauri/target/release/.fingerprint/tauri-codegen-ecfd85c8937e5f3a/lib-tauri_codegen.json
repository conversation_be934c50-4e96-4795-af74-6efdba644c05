{"rustc": 11410426090777951712, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 11247667327328147816, "deps": [[654232091421095663, "tauri_utils", false, 10022422225827242235], [3060637413840920116, "proc_macro2", false, 3850704295121684476], [3150220818285335163, "url", false, 7445822802986157512], [4899080583175475170, "semver", false, 12462931904875540018], [4974441333307933176, "syn", false, 14302716174997422590], [7170110829644101142, "json_patch", false, 16832709223554916395], [7392050791754369441, "ico", false, 16886266557314041753], [8319709847752024821, "uuid", false, 3961784610217971211], [8569119365930580996, "serde_json", false, 6942067756965010790], [9556762810601084293, "brotli", false, 3686830121530367345], [9689903380558560274, "serde", false, 5351880197929074958], [9857275760291862238, "sha2", false, 17561192687204226637], [10806645703491011684, "thiserror", false, 11327448251985432420], [12687914511023397207, "png", false, 4465249767200152524], [13077212702700853852, "base64", false, 8482375644347581409], [15622660310229662834, "walkdir", false, 6540311310246530975], [17990358020177143287, "quote", false, 8511355714600800023]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-codegen-ecfd85c8937e5f3a/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}