{"rustc": 11410426090777951712, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 15531375561524983371, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-runtime-wry-97e179964396cb49/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}