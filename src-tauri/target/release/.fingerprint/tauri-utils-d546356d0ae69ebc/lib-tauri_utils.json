{"rustc": 11410426090777951712, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 1369601567987815722, "path": 16353767327527989442, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 11658487256829999307], [3060637413840920116, "proc_macro2", false, 3850704295121684476], [3150220818285335163, "url", false, 7445822802986157512], [3191507132440681679, "serde_untagged", false, 9046463947707646058], [4071963112282141418, "serde_with", false, 15868900902738194627], [4899080583175475170, "semver", false, 12462931904875540018], [5986029879202738730, "log", false, 7416364068503803003], [6606131838865521726, "ctor", false, 14173242265104357884], [6913375703034175521, "schemars", false, 5847459251798291741], [7170110829644101142, "json_patch", false, 16832709223554916395], [8319709847752024821, "uuid", false, 3961784610217971211], [8569119365930580996, "serde_json", false, 6942067756965010790], [9010263965687315507, "http", false, 2776399017582323651], [9090328626728818999, "toml", false, 778415984354411532], [9451456094439810778, "regex", false, 2288419613006447050], [9556762810601084293, "brotli", false, 3686830121530367345], [9689903380558560274, "serde", false, 5351880197929074958], [10806645703491011684, "thiserror", false, 11327448251985432420], [11655476559277113544, "cargo_metadata", false, 15814576117684677054], [11989259058781683633, "dunce", false, 15487419374939756167], [13625485746686963219, "anyhow", false, 12208565187802399050], [14232843520438415263, "html5ever", false, 17175174774605479448], [15088007382495681292, "kuchiki", false, 10167504511427831229], [15622660310229662834, "walkdir", false, 6540311310246530975], [15932120279885307830, "memchr", false, 12391521909346400758], [17146114186171651583, "infer", false, 4051313345167256095], [17155886227862585100, "glob", false, 18124508793629605632], [17186037756130803222, "phf", false, 11839373781756447522], [17990358020177143287, "quote", false, 8511355714600800023]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-utils-d546356d0ae69ebc/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}