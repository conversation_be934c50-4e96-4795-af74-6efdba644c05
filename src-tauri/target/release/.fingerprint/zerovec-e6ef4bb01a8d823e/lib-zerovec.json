{"rustc": 11410426090777951712, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 1369601567987815722, "path": 7963563453658191281, "deps": [[9620753569207166497, "zerovec_derive", false, 7080197000536405854], [10706449961930108323, "yoke", false, 301316633228500215], [17046516144589451410, "zerofrom", false, 616065663040136051]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zerovec-e6ef4bb01a8d823e/dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}