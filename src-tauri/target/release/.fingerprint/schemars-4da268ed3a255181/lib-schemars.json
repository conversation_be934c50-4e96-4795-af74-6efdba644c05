{"rustc": 11410426090777951712, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 1369601567987815722, "path": 10603290422046522189, "deps": [[3150220818285335163, "url", false, 7445822802986157512], [6913375703034175521, "build_script_build", false, 14214364329984049040], [8319709847752024821, "uuid1", false, 3961784610217971211], [8569119365930580996, "serde_json", false, 6942067756965010790], [9122563107207267705, "dyn_clone", false, 1286412704977057023], [9689903380558560274, "serde", false, 5351880197929074958], [14923790796823607459, "indexmap", false, 6563625529800564119], [16071897500792579091, "schemars_derive", false, 6319234381069608091]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/schemars-4da268ed3a255181/dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}