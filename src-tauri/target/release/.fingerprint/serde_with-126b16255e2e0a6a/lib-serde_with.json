{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"schemars_1\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 722405520834827680, "path": 15349464151003930089, "deps": [[7026957619838884710, "serde_with_macros", false, 15307608061720911310], [9689903380558560274, "serde", false, 5351880197929074958], [16257276029081467297, "serde_derive", false, 12338038698583066858]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/serde_with-126b16255e2e0a6a/dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}