{"rustc": 11410426090777951712, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 6423221176895730690, "deps": [[654232091421095663, "tauri_utils", false, 10022422225827242235], [2704937418414716471, "tauri_codegen", false, 12623691597773784128], [3060637413840920116, "proc_macro2", false, 3850704295121684476], [4974441333307933176, "syn", false, 14302716174997422590], [13077543566650298139, "heck", false, 12143741045076549127], [17990358020177143287, "quote", false, 8511355714600800023]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-macros-bb80591abfd0b0bd/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}