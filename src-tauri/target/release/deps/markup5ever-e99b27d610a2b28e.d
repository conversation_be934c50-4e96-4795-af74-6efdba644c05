/home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/deps/markup5ever-e99b27d610a2b28e.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/data/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/interface/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/interface/tree_builder.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/serialize.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/util/buffer_queue.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/util/smallcharset.rs /home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/build/markup5ever-1d1e2a3d56e694f3/out/generated.rs /home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/build/markup5ever-1d1e2a3d56e694f3/out/named_entities.rs

/home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/deps/libmarkup5ever-e99b27d610a2b28e.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/data/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/interface/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/interface/tree_builder.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/serialize.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/util/buffer_queue.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/util/smallcharset.rs /home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/build/markup5ever-1d1e2a3d56e694f3/out/generated.rs /home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/build/markup5ever-1d1e2a3d56e694f3/out/named_entities.rs

/home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/deps/libmarkup5ever-e99b27d610a2b28e.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/data/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/interface/mod.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/interface/tree_builder.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/serialize.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/util/buffer_queue.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/util/smallcharset.rs /home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/build/markup5ever-1d1e2a3d56e694f3/out/generated.rs /home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/build/markup5ever-1d1e2a3d56e694f3/out/named_entities.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/lib.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/data/mod.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/interface/mod.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/interface/tree_builder.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/serialize.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/util/buffer_queue.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/markup5ever-0.14.1/util/smallcharset.rs:
/home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/build/markup5ever-1d1e2a3d56e694f3/out/generated.rs:
/home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/build/markup5ever-1d1e2a3d56e694f3/out/named_entities.rs:

# env-dep:OUT_DIR=/home/<USER>/Office/Personal/offline-productivity-system/src-tauri/target/release/build/markup5ever-1d1e2a3d56e694f3/out
